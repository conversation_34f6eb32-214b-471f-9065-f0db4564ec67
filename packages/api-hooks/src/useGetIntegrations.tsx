import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";
import { MemberProfile } from "@/api-hooks/useGetMemberProfileMe";

type MemberProfiles = {
  members: MemberProfile[];
};

export const useGetIntegrations = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["integrations"],
    queryFn: async () => {
      return await apiClient.get<any>(`/workspace/integrations`);
    },
  });
};
