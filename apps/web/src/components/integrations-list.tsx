"use client";

import { useGetIntegrations } from "@/api-hooks/useGetIntegrations";
import * as React from "react";
import { Search, Filter, X, Unplug } from "lucide-react";
import { But<PERSON> } from "@/web/components/ui/button";
import { Input } from "@/web/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/web/components/ui/card";

interface Integration {
  integrations?: any[];
  categories?: any[];
}

export function IntegrationsList() {
  const { data: integrations, isLoading: isLoadingIntegrations } =
    useGetIntegrations();

  console.log(
    "-------------------------------------------------------------------",
  );
  console.log(integrations);
  console.log(
    "-------------------------------------------------------------------",
  );

  if (isLoadingIntegrations) {
    return <div>Loading...</div>;
  }

  // Separate active and available integrations
  const activeIntegrations = integrations.activeIntegrations;
  const availableIntegrations = integrations.availableIntegrations;

  return (
    <div className="space-y-8">
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-semibold">Integrations</h3>
          <p className="text-muted-foreground text-sm">
            Connect your favorite tools and services to streamline your
            workflow.
          </p>
        </div>
      </div>

      {activeIntegrations.length > 0 && (
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-semibold">
              Active integrations ({activeIntegrations.length})
            </h3>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {activeIntegrations.map((integration, i) => (
              <IntegrationCard
                key={i}
                integration={integration}
                isActive={true}
              />
            ))}
          </div>
        </div>
      )}

      {/* Available Integrations */}
      {availableIntegrations.length > 0 && (
        <div className="space-y-4">
          <div>
            <h3 className="text-base font-semibold">
              Available integrations ({availableIntegrations.length})
            </h3>
          </div>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {availableIntegrations.map((integration, i) => (
              <IntegrationCard
                key={i}
                integration={integration}
                isActive={false}
              />
            ))}
          </div>
        </div>
      )}

      {/* No Results */}
      {/* {filteredIntegrations.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Search className="text-muted-foreground mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-semibold">
              No integrations found
            </h3>
            <p className="text-muted-foreground mb-4 text-center">
              Try adjusting your search terms or filters to find what
              you&apos;re looking for.
            </p>
            <Button variant="outline" onClick={clearFilters}>
              Clear all filters
            </Button>
          </CardContent>
        </Card>
      )} */}
    </div>
  );
}

interface IntegrationCardProps {
  integration: any;
  isActive: boolean;
}

function IntegrationCard({ integration, isActive }: IntegrationCardProps) {
  return (
    <Card className="flex h-full flex-col">
      <CardHeader className="flex-1 pb-3">
        <div className="flex h-full items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-base">{integration.name}</CardTitle>
            <CardDescription className="mt-1">
              {integration.description}
            </CardDescription>
          </div>

          <div
            className="ml-3 flex h-10 w-10 flex-shrink-0 items-center justify-center overflow-hidden rounded-lg"
            style={{ backgroundColor: integration.color + "20" }}
          ></div>
        </div>
      </CardHeader>

      <CardContent className="mt-auto pt-0">
        {/* Action Button */}
        {isActive ? (
          <div className="flex gap-2">
            <Button variant="outline" className="flex-1">
              Active
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                // Handle disconnect
                console.log("Disconnect:", integration.id);
              }}
              title="Disconnect"
            >
              <Unplug className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <Button
            variant="default"
            className="w-full"
            onClick={() => {
              // Handle connect
              console.log("Connect:", integration.id);
            }}
          >
            Connect
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
